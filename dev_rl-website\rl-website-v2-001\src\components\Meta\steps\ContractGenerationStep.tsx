import { useState } from 'react';
import { Card } from '@/ui/Card';
import { Button } from '@/ui/Button';
import { ContractStepProps } from '@/lib/types/contract';
import { ArrowLeft, Download, Eye, FileText, CheckCircle } from 'lucide-react';

const ContractGenerationStep = ({ formData, onPrev }: ContractStepProps) => {
  const [showPreview, setShowPreview] = useState(false);

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('no-NO', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const generateContract = () => {
    const contractHTML = `
<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arbeidskontrakt - ${formData.employeeName}</title>
    <style>
        @page {
            margin: 2cm;
            size: A4;
        }
        body {
            font-family: <PERSON><PERSON>ri, Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #1e9545;
            padding-bottom: 20px;
        }
        .company-name {
            font-size: 18pt;
            font-weight: bold;
            color: #1e9545;
            margin-bottom: 5px;
        }
        .document-title {
            font-size: 16pt;
            font-weight: bold;
            margin-top: 15px;
        }
        .section {
            margin-bottom: 20px;
        }
        .section-title {
            font-size: 12pt;
            font-weight: bold;
            color: #1e9545;
            margin-bottom: 10px;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 5px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        .info-item {
            margin-bottom: 8px;
        }
        .label {
            font-weight: bold;
            display: inline-block;
            min-width: 120px;
        }
        .signature-section {
            margin-top: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }
        .signature-box {
            border-top: 1px solid #333;
            padding-top: 10px;
            text-align: center;
        }
        @media print {
            body { print-color-adjust: exact; }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">${formData.companyName}</div>
        <div>Org.nr: ${formData.companyOrgNumber}</div>
        <div>${formData.companyAddress}</div>
        <div class="document-title">ARBEIDSKONTRAKT</div>
    </div>

    <div class="section">
        <div class="section-title">1. PARTENES IDENTITET</div>
        <div class="info-grid">
            <div>
                <div class="info-item"><span class="label">Arbeidsgiver:</span> ${formData.companyName}</div>
                <div class="info-item"><span class="label">Org.nr:</span> ${formData.companyOrgNumber}</div>
                <div class="info-item"><span class="label">Adresse:</span> ${formData.companyAddress}</div>
            </div>
            <div>
                <div class="info-item"><span class="label">Arbeidstaker:</span> ${formData.employeeName}</div>
                <div class="info-item"><span class="label">Fødselsdato:</span> ${formatDate(formData.employeeBirthDate)}</div>
                <div class="info-item"><span class="label">Adresse:</span> ${formData.employeeAddress}</div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">2. ARBEIDSSTED OG ARBEIDSOPPGAVER</div>
        <div class="info-item"><span class="label">Arbeidssted:</span> Prosjektbasert innen Ringerike og omegn; oppmøtested avtales for hvert prosjekt</div>
        <div class="info-item"><span class="label">Stillingsbetegnelse:</span> ${formData.position}</div>
        <div class="info-item"><span class="label">Arbeidsoppgaver:</span> Arbeid innen anleggsgartner- og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten</div>
    </div>

    <div class="section">
        <div class="section-title">3. ANSETTELSESFORHOLD</div>
        <div class="info-item"><span class="label">Tiltredelsesdato:</span> ${formatDate(formData.startDate)}</div>
        <div class="info-item"><span class="label">Ansettelsestype:</span> ${formData.employmentType === 'fast' ? 'Fast ansettelse' : 'Midlertidig ansettelse'}</div>
        ${formData.isTemporary ? `
        <div class="info-item"><span class="label">Sluttdato:</span> ${formatDate(formData.temporaryEndDate)}</div>
        <div class="info-item"><span class="label">Begrunnelse:</span> ${formData.temporaryReason}</div>
        ` : ''}
        ${formData.probationPeriod ? `
        <div class="info-item"><span class="label">Prøvetid:</span> ${formData.probationMonths} måneder med 14 dagers gjensidig oppsigelsesfrist</div>
        ` : ''}
    </div>

    <div class="section">
        <div class="section-title">4. ARBEIDSTID OG LØNN</div>
        <div class="info-item"><span class="label">Arbeidstid:</span> ${formData.workingHoursPerWeek} timer per uke, normalt ${formData.workingTime}</div>
        <div class="info-item"><span class="label">Pauser:</span> ${formData.breakTime}</div>
        <div class="info-item"><span class="label">Timelønn:</span> kr ${formData.hourlyRate},-</div>
        <div class="info-item"><span class="label">Overtidstillegg:</span> ${formData.overtimeRate}% av timelønn</div>
        <div class="info-item"><span class="label">Utbetaling:</span> Den ${formData.paymentDay}. hver måned til kontonummer ${formData.accountNumber}</div>
        ${formData.ownTools ? `<div class="info-item"><span class="label">Verktøygodtgjørelse:</span> ${formData.toolAllowance}</div>` : ''}
        <div class="info-item"><span class="label">Kjøregodtgjørelse:</span> ${formData.travelAllowance}</div>
    </div>

    <div class="section">
        <div class="section-title">5. FERIE OG PERMISJON</div>
        <div class="info-item"><span class="label">Ferie:</span> 5 uker per år i henhold til ferieloven</div>
        <div class="info-item"><span class="label">Feriepenger:</span> 12% av feriepengegrunnlaget</div>
        <div class="info-item"><span class="label">Sykepenger:</span> Arbeidsgiver dekker lønn i arbeidsgiverperioden ved sykdom</div>
    </div>

    <div class="section">
        <div class="section-title">6. OPPSIGELSE OG ENDRINGER</div>
        <div class="info-item"><span class="label">Oppsigelsesfrister:</span> ${formData.noticePeriod}</div>
        <div class="info-item"><span class="label">Varslingsregler:</span> ${formData.notificationRules}</div>
        <div class="info-item"><span class="label">Formkrav:</span> Oppsigelse skal være skriftlig</div>
    </div>

    <div class="section">
        <div class="section-title">7. PENSJON OG FORSIKRING</div>
        <div class="info-item"><span class="label">Pensjon:</span> ${formData.pensionProvider} (org.nr ${formData.pensionOrgNumber})</div>
        <div class="info-item"><span class="label">Yrkesskadeforsikring:</span> ${formData.insuranceProvider} (org.nr ${formData.insuranceOrgNumber})</div>
    </div>

    <div class="section">
        <div class="section-title">8. ØVRIGE BESTEMMELSER</div>
        <div class="info-item"><span class="label">Tariffavtale:</span> Ingen tariffavtale er gjeldende per dags dato</div>
        <div class="info-item"><span class="label">Kompetanseutvikling:</span> Arbeidsgiver tilbyr nødvendig opplæring og vil vurdere kompetanseutviklingstiltak for stillingen</div>
    </div>

    <p style="margin-top: 30px; font-size: 10pt; color: #666;">
        Denne kontrakten er utarbeidet i henhold til Arbeidsmiljøloven § 14-6 og oppfyller alle juridiske krav per ${new Date().toLocaleDateString('no-NO')}.
    </p>

    <div class="signature-section">
        <div class="signature-box">
            <div>Dato: _________________</div>
            <br><br>
            <div>Arbeidsgiver</div>
            <div>${formData.companyName}</div>
        </div>
        <div class="signature-box">
            <div>Dato: _________________</div>
            <br><br>
            <div>Arbeidstaker</div>
            <div>${formData.employeeName}</div>
        </div>
    </div>
</body>
</html>`;

    return contractHTML;
  };

  const handleDownload = () => {
    const contractHTML = generateContract();
    const blob = new Blob([contractHTML], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `Arbeidskontrakt_${formData.employeeName.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handlePrint = () => {
    const contractHTML = generateContract();
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(contractHTML);
      printWindow.document.close();
      printWindow.focus();
      setTimeout(() => {
        printWindow.print();
      }, 250);
    }
  };

  return (
    <div className="space-y-6">
      {/* Summary */}
      <Card title="Sammendrag av kontraktinformasjon" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Kontrakten er klar for generering</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Ansatt</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div><span className="font-medium">Navn:</span> {formData.employeeName}</div>
                <div><span className="font-medium">Stilling:</span> {formData.position}</div>
                <div><span className="font-medium">Startdato:</span> {formatDate(formData.startDate)}</div>
                <div><span className="font-medium">Timelønn:</span> kr {formData.hourlyRate},-</div>
              </div>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Ansettelse</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div><span className="font-medium">Type:</span> {formData.employmentType === 'fast' ? 'Fast ansettelse' : 'Midlertidig ansettelse'}</div>
                <div><span className="font-medium">Arbeidstid:</span> {formData.workingHoursPerWeek} t/uke</div>
                <div><span className="font-medium">Prøvetid:</span> {formData.probationPeriod ? `${formData.probationMonths} måneder` : 'Nei'}</div>
                <div><span className="font-medium">Eget verktøy:</span> {formData.ownTools ? 'Ja' : 'Nei'}</div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Actions */}
      <Card title="Generer arbeidskontrakt" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <FileText className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Last ned eller skriv ut kontrakten</h3>
          </div>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-start">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
              <div className="text-sm">
                <p className="text-green-800 font-medium">Kontrakten er juridisk korrekt</p>
                <p className="text-green-700 mt-1">
                  Denne kontrakten oppfyller alle krav i Arbeidsmiljøloven § 14-6 og er klar for signering.
                </p>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={() => setShowPreview(!showPreview)}
              variant="secondary"
              size="lg"
              className="flex-1"
            >
              <Eye className="h-5 w-5 mr-2" />
              {showPreview ? 'Skjul forhåndsvisning' : 'Vis forhåndsvisning'}
            </Button>
            
            <Button
              onClick={handleDownload}
              variant="primary"
              size="lg"
              className="flex-1"
            >
              <Download className="h-5 w-5 mr-2" />
              Last ned kontrakt
            </Button>
            
            <Button
              onClick={handlePrint}
              variant="primary"
              size="lg"
              className="flex-1"
            >
              <FileText className="h-5 w-5 mr-2" />
              Skriv ut
            </Button>
          </div>
        </div>
      </Card>

      {/* Preview */}
      {showPreview && (
        <Card title="Forhåndsvisning av kontrakt" className="mb-6">
          <div 
            className="border rounded-lg p-6 bg-white max-h-96 overflow-y-auto text-sm"
            dangerouslySetInnerHTML={{ __html: generateContract().replace(/<style>[\s\S]*?<\/style>/, '') }}
          />
        </Card>
      )}

      {/* Navigation */}
      <div className="flex justify-start">
        <Button
          onClick={onPrev}
          variant="secondary"
          size="lg"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          Forrige steg
        </Button>
      </div>
    </div>
  );
};

export default ContractGenerationStep;
